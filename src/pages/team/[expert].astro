---
import Layout from '../../layouts/Layout.astro';
import ExpertProfile from '../../components/ExpertProfile.astro';

export async function getStaticPaths() {
  const experts = {
    'kentaylor': {
      name: '<PERSON>',
      role: 'COO & Founder',
      image: '/team/ken.webp',
      bio: '<PERSON> is an experienced coder with more than a decade of experience in application deployment. He is also well experienced in Business Management, Marketing and Music Production.',
      expertise: ['Cloud Architecture', 'Software Development', 'System Design', 'Technical Leadership', 'DevOps', 'Digital Audio Production'],
      achievements: [
        'Successfully led Lugetech\'s expansion across Guyana',
        'Pioneered innovative solutions for enterprise clients',
      ],
    },
    'andreblair': {
      name: '<PERSON>',
      role: 'CTO & Founder',
      image: '/team/andre.webp',
      bio: '<PERSON> is an Engineering Entrepreneur with a love for problem solving. Having a passion for Business Development he has worked in numerous sectors, such as Investment Research for some of the world\'s largest Hedge Funds. His consultative approach to business is one he enjoys sharing with others',
      expertise: ['Business strategy', 'Digital Transformation', 'Business Development', 'Technology Innovation', 'Team Leadership'],
      achievements: [
        'Secured key partnerships with industry leaders',
        'Led the development of numerous enterprise-scale applications',
      ],
    },
    'clinteastman': {
      name: '<PERSON>',
      role: 'CTO & Founder',
      image: '/team/clint.webp',
      bio: 'Clint is an experienced coder with extensive experience in software development. He has worked as a Solutions Architect for Tech Giant Amazon and is also a US War Veteran, Paratrooper and Demolitions Expert.',
      expertise: ["Cloud Architecture", 'Full-stack Development', 'UI/UX Design', 'Agile Methodologies', 'Team Management', 'Technical Architecture'],
      achievements: [
        'Led the development of our flagship product suite',
        'Contributed to multiple open-source projects'
      ],
    },
    'ivanatomic': {
      name: 'Ivana Tomic',
      role: 'Cyber Security and Telecommunications Expert',
      image: '/team/ivana.webp',
      bio: `Dr Ivana Tomić a Senior Lecturer in Cyber Security (equivalent to an Associate Professor US) and a member of the Centre for Sustainable Cyber Security (CS2) at the University of Greenwich. Her research interests span the areas of cyber security, control engineering, and wireless communication networks, with a focus on building trusted, secure, and resilient Internet of Things and Cyber Physical Systems. 

She has published her research in top-tier journals, including ACM TCPS, IEEE TII and IEEE IoT. She is the Editor at Simulation
Modeling Practice and Theory Journal, Elsevier.

Before joining the University of Greenwich, she was a Research Associate in the Computing Department at Imperial College London (AESE research group). During her time at Imperial College, she worked as a security researcher on the S4: Science of Sensor Systems Software EPSRC programme grant and the IoT in the Park project as a part of the EPSRC PETRAS IoT research hub. Also, she was a Co-I on Fog to the Field project championed by Cisco.

Dr Tomić received a PhD in Control Engineering from City, University of London in 2016`,
      expertise: ['Cyber Security (Core Expertise)', 'Cyber-Physical Systems (CPS) Security (Specific Area of Focus)', 'Market Analysis', 'Trusted Systems Design'],
      achievements: [
        'Led research team in the development of a resilient architecture for Cyber-Physical Systems',
        'Led cross-functional teams of 20+ members'
      ],
    },
    'kiewartgulliver': {
      name: 'Keiwart Gulliver',
      role: 'Game Dev / UI Expert',
      image: '/team/kilo.webp',
      bio: 'Kiewart is a talanted Game Developer with a passion for creating immersive experiences.',
      expertise: ['Game Development', 'UI/UX Design', '3D Modelling', 'Animation', 'C#, C++ Programming'],
      achievements: [
        'Developed award-winning games',
        'Led the development of our VR/AR product suite'
      ],
    },
    'sunaika': {
      name: 'Sunaika Adolphus',
      role: 'Business Development',
      image: '/team/su.webp',
      bio: 'Sunaika Adolphus is a dynamic Creative Director with seven years of experience in customer service and the creative industry, both locally and internationally. With a strong background as a remote Executive Assistant for global companies, Sunaika specializes in building brands and fostering meaningful relationships between businesses. Known for being friendly, fun, and innovative, Sunaika brings a fresh perspective and a collaborative spirit to every project, helping organizations connect, grow, and stand out.',
      expertise: ['Creative Direction', 'Brand Building', 'Remote Executive Support', 'Collaboration & Innovation'],
      achievements: [
        'Led the development and launch of multiple brand identities for businesses, enhancing their market presence locally and internationally.',
        'Designed and executed creative campaigns that boosted client engagement and business growth.'
      ],
    },
  };

  return Object.entries(experts).map(([expert, data]) => ({
    params: { expert },
    props: { expertData: data }
  }));
}

const { expertData } = Astro.props;
---

<Layout title={`${expertData.name} - Team Member`}>
  <ExpertProfile {...expertData} />
</Layout>