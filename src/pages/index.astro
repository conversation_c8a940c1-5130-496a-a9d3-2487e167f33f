---
import Layout from "../layouts/Layout.astro";
import HeroBackground from "../components/HeroBackground.jsx";
import Testimonials from "../components/Testimonials.jsx";
import ScrollFadeSection from "../components/ScrollFadeSection.astro";
import { Image } from 'astro:assets';
import { generatePageSEO } from '../seo.config.ts';

const seoData = generatePageSEO({
	title: 'Home',
	description: 'Lugetech (NOT Logitech) - Guyana\'s Premier Technology Company. Leading software development, IT services, and digital solutions for Guyanese businesses in Georgetown, New Amsterdam, Linden, and nationwide.',
	keywords: ['Guyana tech company', 'Georgetown software development', 'Caribbean IT services', 'Guyanese technology solutions']
});
---

<Layout title="Home">
	<!-- Hero Section -->
	<section
		class="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-indigo-900 to-purple-900 overflow-hidden"
	>
		<HeroBackground client:visible />
		<div class="absolute inset-0 bg-[url('/src/assets/background.svg')] opacity-5"></div>
		<div class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
		
		<div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
			<div class="text-center space-y-8 animate-fade-in">
				<div class="space-y-4">
					<h1 class="text-5xl md:text-7xl font-bold tracking-tight">
						<span class="block bg-gradient-to-r from-white via-indigo-200 to-purple-200 bg-clip-text text-transparent">
							Lugetech: Guyana's
						</span>
						<span class="block bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent mt-2">
							Premier Technology Company
						</span>
					</h1>
					<p class="max-w-3xl mx-auto text-lg md:text-xl text-gray-300 leading-relaxed">
						Serving Georgetown, New Amsterdam, Linden, and all of Guyana with
						cutting-edge software development, web applications, mobile apps, and
						digital transformation services. Your trusted Guyanese technology partner.
					</p>
				</div>
				
				<div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
					<a
						href="/contact"
						class="group relative px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-glow-lg transition-all duration-300 transform hover:scale-105"
					>
						<span class="relative z-10">Get Started</span>
						<div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
					</a>
					<a
						href="/services"
						class="group px-8 py-4 border-2 border-gray-600 text-gray-300 font-semibold rounded-xl hover:border-indigo-500 hover:text-white transition-all duration-300"
					>
						Learn More
					</a>
				</div>
			</div>
		</div>
		
		<div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
			<svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
			</svg>
		</div>
	</section>



	<!-- Features Section -->
	<ScrollFadeSection as="section" class="py-12 bg-gray-800" direction="up" delay={100}>
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="lg:text-center">
				<h2
					class="text-base text-indigo-400 font-semibold tracking-wide uppercase"
				>
					Our Services
				</h2>
				<p
					class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-white sm:text-4xl"
				>
					Better Solutions for Your Business
				</p>
			</div>

			<div class="mt-16">
				<div
					class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3"
				>
					{[
						{
							icon: "M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4",
							title: "Custom Software Development",
							description: "Tailored solutions designed to meet your specific business needs and challenges.",
							color: "from-indigo-500 to-purple-600"
						},
						{
							icon: "M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",
							title: "Cloud Solutions",
							description: "Secure and scalable cloud infrastructure to power your business applications.",
							color: "from-purple-500 to-pink-600"
						},
						{
							icon: "M13 10V3L4 14h7v7l9-11h-7z",
							title: "Digital Transformation",
							description: "Modernize your business processes with cutting-edge digital solutions.",
							color: "from-cyan-500 to-blue-600"
						}
					].map(({ icon, title, description, color }) => (
						<div class="group relative">
							<div class="absolute inset-0 bg-gradient-to-r {color} rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-xl"></div>
							<div class="relative p-8 bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl hover:border-gray-600/50 transition-all duration-300 hover:shadow-2xl hover:shadow-gray-900/50">
								<div class="flex items-center justify-center h-16 w-16 rounded-xl bg-gradient-to-r {color} text-white mb-6 shadow-lg">
									<svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={icon}></path>
									</svg>
								</div>
								<h3 class="text-2xl font-bold text-white mb-3">{title}</h3>
								<p class="text-gray-400 leading-relaxed">{description}</p>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	</ScrollFadeSection>

	<!-- Team Section -->
	<ScrollFadeSection as="section" class="py-24 bg-gradient-to-b from-gray-900 to-black" direction="up" delay={200}>
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-16">
				<h2 class="text-sm font-semibold text-indigo-400 uppercase tracking-wider">
					Our Team
				</h2>
				<h3 class="mt-4 text-4xl md:text-5xl font-extrabold text-white">
					Meet the <span class="text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-400">Experts</span>
				</h3>
				<p class="mt-4 max-w-2xl mx-auto text-lg text-gray-400">
					The brilliant minds behind Lugetech's success
				</p>
			</div>

			<div
				class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto"
			>
				<a
					href="/team/kentaylor"
					class="text-center group cursor-pointer block transform transition-all duration-300 hover:-translate-y-2"
				>
					<div
						class="relative h-48 w-48 rounded-xl bg-gradient-to-br from-indigo-400 to-purple-500 mx-auto mb-6 transform transition-all duration-300 group-hover:scale-105 group-hover:rotate-3 overflow-hidden shadow-lg"
					>
						<img
							src="/team/ken.webp"
							alt="Ken Taylor"
							class="w-full h-full object-cover opacity-90 group-hover:opacity-100 transition-opacity duration-300"
						/>
					</div>
					<h3
						class="text-xl font-bold text-white mb-1 group-hover:text-indigo-400 transition-colors duration-300"
					>
						Ken Taylor
					</h3>
					<p class="text-indigo-600 mb-3">COO & Founder</p>
				</a>
				<a
					href="/team/andreblair"
					class="text-center group cursor-pointer block transform transition-all duration-300 hover:-translate-y-2"
				>
					<div
						class="relative h-48 w-48 rounded-xl bg-gradient-to-br from-indigo-400 to-purple-500 mx-auto mb-6 transform transition-all duration-300 group-hover:scale-105 group-hover:rotate-3 overflow-hidden shadow-lg"
					>
						<img
							src="/team/andre.webp"
							alt="Andre Blair"
							class="w-full h-full object-cover opacity-90 group-hover:opacity-100 transition-opacity duration-300"
						/>
					</div>
					<h3
						class="text-xl font-bold text-white mb-1 group-hover:text-indigo-400 transition-colors duration-300"
					>
						Andre Blair
					</h3>
					<p class="text-indigo-600 mb-3">CTO & Founder</p>
				</a>
				<a
					href="/team/clinteastman"
					class="text-center group cursor-pointer block transform transition-all duration-300 hover:-translate-y-2"
				>
					<div
						class="relative h-48 w-48 rounded-xl bg-gradient-to-br from-indigo-400 to-purple-500 mx-auto mb-6 transform transition-all duration-300 group-hover:scale-105 group-hover:rotate-3 overflow-hidden shadow-lg"
					>
						<img
							src="/team/clint.webp"
							alt="Clint Eastman"
							class="w-full h-full object-cover opacity-90 group-hover:opacity-100 transition-opacity duration-300"
						/>
					</div>
					<h3
						class="text-xl font-bold text-white mb-1 group-hover:text-indigo-400 transition-colors duration-300"
					>
						Clint Eastman
					</h3>
					<p class="text-indigo-600 mb-3">Lead Developer</p>
				</a>
			</div>
			<div class="mt-12 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
				<a
					href="/team/ivanatomic"
					class="text-center group cursor-pointer block transform transition-all duration-300 hover:-translate-y-2"
				>
					<div
						class="relative h-48 w-48 rounded-xl bg-gradient-to-br from-indigo-400 to-purple-500 mx-auto mb-6 transform transition-all duration-300 group-hover:scale-105 group-hover:rotate-3 overflow-hidden shadow-lg"
					>
						<img
							src="/team/ivana.webp"
							alt="Ivana Tomic"
							class="w-full h-full object-cover opacity-90 group-hover:opacity-100 transition-opacity duration-300"
						/>
					</div>
					<h3
						class="text-xl font-bold text-white mb-1 group-hover:text-indigo-400 transition-colors duration-300"
					>
						Ivana Tomic
					</h3>
					<p class="text-indigo-600 mb-3">
						Cyber Security and Telecommunications Expert
					</p>
				</a>
				<a
					href="/team/kiewartgulliver"
					class="text-center group cursor-pointer block transform transition-all duration-300 hover:-translate-y-2"
				>
					<div
						class="relative h-48 w-48 rounded-xl bg-gradient-to-br from-indigo-400 to-purple-500 mx-auto mb-6 transform transition-all duration-300 group-hover:scale-105 group-hover:rotate-3 overflow-hidden shadow-lg"
					>
						<img
							src="/team/kilo.webp"
							alt="Kilo"
							class="w-full h-full object-cover opacity-90 group-hover:opacity-100 transition-opacity duration-300"
						/>
					</div>
					<h3
						class="text-xl font-bold text-white mb-1 group-hover:text-indigo-400 transition-colors duration-300"
					>
						Kiewart Gulliver
					</h3>
					<p class="text-indigo-600 mb-3">Game Dev / UI Expert</p>
				</a>
				<a
					href="/team/sunaika"
					class="text-center group cursor-pointer block transform transition-all duration-300 hover:-translate-y-2"
				>
					<div
						class="relative h-48 w-48 rounded-xl bg-gradient-to-br from-indigo-400 to-purple-500 mx-auto mb-6 transform transition-all duration-300 group-hover:scale-105 group-hover:rotate-3 overflow-hidden shadow-lg"
					>
						<img
							src="/team/su.webp"
							alt="Sunaika Adolphus"
							class="w-full h-full object-cover opacity-90 group-hover:opacity-100 transition-opacity duration-300"
						/>
					</div>
					<h3
						class="text-xl font-bold text-white mb-1 group-hover:text-indigo-400 transition-colors duration-300"
					>
						Sunaika Adolphus
					</h3>
					<p class="text-indigo-600 mb-3">Business Development</p>
				</a>
			</div>
			<Testimonials client:load />

			<!-- CTA Section -->
			<ScrollFadeSection
				as="section"
				class="relative overflow-hidden"
				direction="up"
				delay={300}
			>
				<div class="absolute inset-0 bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 opacity-90"></div>
				<div class="absolute inset-0 bg-[url('/src/assets/background.svg')] opacity-10"></div>
				<div class="relative max-w-7xl mx-auto py-20 px-4 sm:px-6 lg:py-24 lg:px-8">
					<div class="text-center">
						<h2 class="text-4xl font-extrabold tracking-tight text-white sm:text-5xl md:text-6xl">
							<span class="block">Ready to work with Lugetech Guyana?</span>
							<span class="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-200 to-purple-200 text-2xl mt-4">
								Contact the leading tech company in Guyana today for a free consultation.
							</span>
						</h2>
						<div class="mt-10 flex justify-center">
							<a
								href="/contact"
								class="group relative inline-flex items-center justify-center px-8 py-4 text-lg font-medium rounded-full text-white bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 transform hover:scale-105 transition-all duration-300 shadow-2xl hover:shadow-indigo-500/25"
							>
								<span class="absolute inset-0 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-xl"></span>
								<span class="relative">Get in touch</span>
							</a>
						</div>
					</div>
				</div>
			</ScrollFadeSection>
		</div>
	</ScrollFadeSection>
</Layout>
